# ========== STAGE 1: CONFIGURATION & INITIALIZATION ==========
import os
import json
import re
import uuid
from pathlib import Path
from tqdm import tqdm
import pandas as pd
from collections import defaultdict
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
import threading

# Tree-sitter for AST parsing
from tree_sitter import Language, Parser
import tree_sitter_java as tsjava

# LangChain components
from langchain_community.document_loaders import TextLoader
from langchain.text_splitter import RecursiveCharacterTextSplitter, Language as LC_Language
from langchain_experimental.graph_transformers import LLMGraphTransformer
from langchain_community.graphs import Neo4jGraph
from langchain_openai import AzureChatOpenAI
from langchain_core.prompts import PromptTemplate
from langchain.schema import Document

# Configuration
BASE_PATH = Path(r"C:/Shaik/sample/LineageTestProject")

# Neo4j Configuration
NEO4J_URI = "bolt://localhost:7687"
NEO4J_USER = "neo4j"
NEO4J_PASSWORD = "Test@7889"
NEO4J_DB = "test"

# Initialize connections
graph = Neo4jGraph(url=NEO4J_URI, username=NEO4J_USER, password=NEO4J_PASSWORD, database=NEO4J_DB)
JAVA_LANGUAGE = Language(tsjava.language())
parser = Parser(JAVA_LANGUAGE)

# Azure OpenAI Configuration
llm = AzureChatOpenAI(
    api_key="********************************",
    azure_endpoint="https://azureopenaibrsc.openai.azure.com/",
    azure_deployment="gpt-4o",
    api_version="2024-12-01-preview"
)

# Temp variables to filter out
TEMP_VARIABLES = {
    'i', 'j', 'k', 'l', 'm', 'n', 'x', 'y', 'z',
    'temp', 'tmp', 'temporary', 'temp1', 'temp2',
    'count', 'counter', 'index', 'idx', 'iter',
    'result', 'res', 'ret', 'val', 'value',
    'item', 'elem', 'element', 'obj', 'object',
    'str', 'string', 'num', 'number', 'flag',
    'bool', 'boolean', 'arr', 'array', 'list',
    'map', 'set', 'data', 'info', 'param', 'arg','Status'
}

# Memory storage
MEMORY_FILE = "lineage_memory_v9.json"
memory_lock = threading.Lock()

def load_memory():
    try:
        with open(MEMORY_FILE, 'r', encoding='utf-8') as f:
            data = json.load(f)
            def convert_from_json(obj):
                if isinstance(obj, dict):
                    result = {}
                    for k, v in obj.items():
                        if k == 'validated_edges' and isinstance(v, list):
                            result[k] = set(v)
                        else:
                            result[k] = convert_from_json(v)
                    return result
                elif isinstance(obj, list):
                    return [convert_from_json(item) for item in obj]
                else:
                    return obj
            return convert_from_json(data)
    except FileNotFoundError:
        return {
            'class_registry': {},
            'validated_edges': set(),
            'variable_contexts': {},
            'method_signatures': {}
        }

def save_memory(memory):
    with memory_lock:
        try:
            memory_copy = memory.copy()
            def convert_for_json(obj):
                if isinstance(obj, set):
                    return list(obj)
                elif isinstance(obj, dict):
                    return {k: convert_for_json(v) for k, v in obj.items()}
                elif isinstance(obj, list):
                    return [convert_for_json(item) for item in obj]
                else:
                    return obj
            memory_copy = convert_for_json(memory_copy)
            with open(MEMORY_FILE, 'w', encoding='utf-8') as f:
                json.dump(memory_copy, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"Memory save error: {e}")

# Initialize memory
memory = load_memory()

# Clear Neo4j database
graph.query("MATCH (n) DETACH DELETE n")
print("Stage 1 Complete: Configuration loaded and Neo4j cleared")

# ========== UTILITY FUNCTIONS ==========

def to_pascal_case(text):
    if not text:
        return text
    
    # Remove file extensions first
    text = re.sub(r'\.(java|class)$', '', text, flags=re.IGNORECASE)
    
    # Handle file paths - extract just the filename
    if '/' in text or '\\' in text:
        text = os.path.basename(text)
        text = re.sub(r'\.(java|class)$', '', text, flags=re.IGNORECASE)
    
    # IMPROVED: Don't assume text is already PascalCase - always normalize it
    # This fixes issues like 'Userservice' vs 'UserService'
    
    # Handle camelCase -> PascalCase (e.g., userService -> UserService)
    # Split on capital letters to handle camelCase properly
    if not re.search(r'[_\-\s]', text):  # No delimiters, might be camelCase
        # Split camelCase: userService -> ['user', 'Service']
        camel_parts = re.findall(r'[A-Z]?[a-z]+|[A-Z]+(?=[A-Z][a-z]|\b)', text)
        if camel_parts and len(camel_parts) > 1:
            parts = camel_parts
        else:
            # Single word - ensure proper capitalization
            return text[0].upper() + text[1:].lower() if len(text) > 1 else text.upper()
    else:
        # Split on common delimiters
        parts = re.split(r'[_\-\s]+', text)
    
    # Capitalize each part
    result = ''
    for part in parts:
        if part:
            result += part[0].upper() + part[1:].lower() if len(part) > 1 else part.upper()
    
    return result if result else text

def extract_clean_name(full_name, name_type):
    if not full_name:
        return full_name
    
    # Remove common prefixes
    prefixes = ['method:', 'class:', 'variable:', 'field:', 'table:', 'endpoint:']
    for prefix in prefixes:
        if full_name.lower().startswith(prefix):
            full_name = full_name[len(prefix):]
    
    # Remove file extensions
    full_name = re.sub(r'\.(java|class)$', '', full_name, flags=re.IGNORECASE)
    
    # Handle file.class patterns - extract only class name
    if '.' in full_name and name_type.lower() in ['class', 'interface']:
        parts = full_name.split('.')
        full_name = parts[-1] if parts[-1] else parts[-2] if len(parts) > 1 else full_name
    
    # Handle classname.method patterns - extract only method name
    if '.' in full_name and name_type.lower() == 'method':
        parts = full_name.split('.')
        full_name = parts[-1] if parts[-1] else parts[-2] if len(parts) > 1 else full_name
    
    # Apply PascalCase for classes, methods, files, folders
    if name_type.lower() in ['class', 'interface', 'method', 'file', 'folder']:
        return to_pascal_case(full_name)
    
    # For variables, keep original name (context handled separately)
    if name_type.lower() == 'variable':
        if '.' in full_name:
            return full_name.split('.')[-1]  # Return only variable name
        return full_name
    
    # For tables, apply PascalCase
    if name_type.lower() == 'table':
        return to_pascal_case(full_name)
    
    return full_name

def is_temp_variable(var_name):
    if not var_name:
        return True
    var_lower = var_name.lower().strip()
    return var_lower in TEMP_VARIABLES or len(var_lower) <= 1

def escape_braces_for_langchain(text):
    if not text:
        return text
    return text.replace('{', '{{').replace('}', '}}')

print("✅ Utility functions loaded - ALL Java files will be processed (no file filtering)")

# ========== STAGE 2: FOLDER-FILE HIERARCHY ==========

def extract_folder_file_hierarchy():
    relationships = []
    base_folder = to_pascal_case(BASE_PATH.name)

    for root, dirs, files in os.walk(BASE_PATH):
        current_path = Path(root)
        rel_path = current_path.relative_to(BASE_PATH)

        # Determine current folder name and its parent
        if rel_path != Path('.'):
            folder_name = to_pascal_case(current_path.name)
            parent_rel_path = current_path.parent.relative_to(BASE_PATH)
            parent_name = base_folder if parent_rel_path == Path('.') else to_pascal_case(current_path.parent.name)

            relationships.append({
                'source_node': parent_name,
                'source_type': 'Folder',
                'destination_node': folder_name,
                'destination_type': 'Folder',
                'relationship': 'CONTAINS'
            })
            current_folder_name = folder_name
        else:
            current_folder_name = base_folder

        # Process files inside the folder
        for file in files:
            if file.lower().endswith(".java"):
                file_name = extract_clean_name(file, 'file')
                relationships.append({
                    'source_node': current_folder_name,
                    'source_type': 'Folder',
                    'destination_node': file_name,
                    'destination_type': 'File',
                    'relationship': 'CONTAINS',
                    'file_path': str(current_path / file)
                })

    return relationships

def extract_file_class_relationships_ast(hierarchy_df):
    file_class_relationships = []
    
    # Get all Java files from the hierarchy data
    java_files = hierarchy_df[hierarchy_df['destination_type'] == 'File']
    
    for _, file_row in java_files.iterrows():
        if 'file_path' in file_row and file_row['file_path']:
            file_path = file_row['file_path']
            file_name = file_row['destination_node']
            
            try:
                # Read and parse the Java file
                with open(file_path, 'r', encoding='utf-8') as f:
                    source_code_str = f.read()
                
                # Process ALL Java files - no filtering to ensure complete pipeline coverage
                
                source_code = source_code_str.encode('utf-8')
                tree = parser.parse(source_code)
                root_node = tree.root_node
                
                # Find class declarations
                def find_classes(node):
                    classes = []
                    if node.type == 'class_declaration':
                        # Find class name
                        for child in node.children:
                            if child.type == 'identifier':
                                class_name = source_code[child.start_byte:child.end_byte].decode('utf-8')
                                classes.append(to_pascal_case(class_name))
                                break
                    
                    # Recursively search child nodes
                    for child in node.children:
                        classes.extend(find_classes(child))
                    
                    return classes
                
                classes_in_file = find_classes(root_node)
                
                # Create file-class relationships
                for class_name in classes_in_file:
                    file_class_relationships.append({
                        'source_node': file_name,
                        'source_type': 'File',
                        'destination_node': class_name,
                        'destination_type': 'Class',
                        'relationship': 'DECLARES',
                        'file_path': file_path
                    })
                    
            except Exception as e:
                print(f"Error processing {file_path}: {e}")
                continue
    
    return file_class_relationships

# Execute Stage 2
folder_file_relationships = extract_folder_file_hierarchy()
df_hierarchy = pd.DataFrame(folder_file_relationships)

# Execute Stage 2B: Extract file-class relationships
file_class_relationships = extract_file_class_relationships_ast(df_hierarchy)
df_file_class = pd.DataFrame(file_class_relationships)

# Append file-class relationships to the hierarchy DataFrame
if len(df_file_class) > 0:
    df_hierarchy = pd.concat([df_hierarchy, df_file_class], ignore_index=True)

# Store in memory
for _, row in df_hierarchy.iterrows():
    edge_key = f"{row['source_node']}-{row['relationship']}-{row['destination_node']}"
    memory['validated_edges'].add(edge_key)
save_memory(memory)

print(f"Stage 2 Complete: {len(df_hierarchy)} folder/file relationships extracted")
print(f"Folders: {len([r for r in folder_file_relationships if r['destination_type'] == 'Folder'])}, Files: {len([r for r in folder_file_relationships if r['destination_type'] == 'File'])}")
print(f"File-Class relationships: {len(df_file_class)}")

# ========== STAGE 3: CLASS REGISTRY & AST CONTEXT ==========

# Patterns for analysis
PACKAGE_PATTERN = r'package\s+([\w\.]+);'
IMPORT_PATTERN = r'import\s+([\w\.]+);'
MAPPING_PATTERNS = {
    'GetMapping': r'@GetMapping\s*\(\s*["\']([^"\']+)["\']',
    'PostMapping': r'@PostMapping\s*\(\s*["\']([^"\']+)["\']',
    'PutMapping': r'@PutMapping\s*\(\s*["\']([^"\']+)["\']',
    'DeleteMapping': r'@DeleteMapping\s*\(\s*["\']([^"\']+)["\']',
    'RequestMapping': r'@RequestMapping\s*\(\s*["\']([^"\']+)["\']'
}

def extract_package_and_imports(source_code_str):
    package_match = re.search(PACKAGE_PATTERN, source_code_str)
    package_name = package_match.group(1) if package_match else None
    import_matches = re.findall(IMPORT_PATTERN, source_code_str)
    return package_name, import_matches

def extract_api_endpoints(source_code_str):
    endpoints = []
    for mapping_type, pattern in MAPPING_PATTERNS.items():
        matches = re.findall(pattern, source_code_str, re.MULTILINE)
        for match in matches:
            path = match.strip()
            if path:
                method = mapping_type.replace('Mapping', '').upper() if mapping_type != 'RequestMapping' else 'GET'
                endpoints.append({
                    'type': mapping_type,
                    'path': path,
                    'method': method
                })
    return endpoints

def extract_database_entities(source_code_str):
    entities = []

    # @Entity/@Table extraction
    if "@Entity" in source_code_str:
        table_matches = re.findall(r'@Table\s*\(\s*name\s*=\s*["\']([^"\']+)["\']', source_code_str)
        for table_name in table_matches:
            entities.append({'type': 'table', 'name': table_name.strip()})

        if not table_matches:
            class_match = re.search(r'(public\s+)?(class|abstract class|interface)\s+(\w+)', source_code_str)
            if class_match:
                class_name = class_match.group(3)
                snake_case = re.sub('([a-z0-9])([A-Z])', r'\1_\2', class_name).lower()
                entities.append({'type': 'table', 'name': snake_case})

    # @Query: detect raw SQL or JPQL references to tables
    query_pattern = r'@Query\s*\([^)]*["\']([^"\']*(?:FROM|from)\s+([\w]+)[^"\']*)["\']'
    query_matches = re.findall(query_pattern, source_code_str, re.MULTILINE | re.IGNORECASE)
    for _, table in query_matches:
        table = table.strip()
        if table and table.lower() not in {'select', 'where', 'group', 'order'}:
            entities.append({'type': 'table', 'name': table})

    return entities

def build_class_registry():
    class_registry = {}
    
    for root, _, files in os.walk(BASE_PATH):
        for file in files:
            if file.endswith('.java'):
                file_path = os.path.join(root, file)
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        source_code_str = f.read()
                    
                    # Process ALL Java files - no filtering to ensure complete pipeline coverage
                    
                    package_name, imports = extract_package_and_imports(source_code_str)
                    endpoints = extract_api_endpoints(source_code_str)
                    db_entities = extract_database_entities(source_code_str)
                    
                    # Apply improved name cleaning
                    class_name = extract_clean_name(file.replace('.java', ''), 'class')
                    fqcn = f'{package_name}.{class_name}' if package_name else class_name
                    
                    class_registry[class_name] = {
                        'fqcn': fqcn,
                        'package': package_name,
                        'file_path': file_path,
                        'imports': imports,
                        'endpoints': endpoints,
                        'db_entities': db_entities,
                        'source_code': source_code_str
                    }
                    
                    # Store method signatures in memory for cross-stage reference
                    methods = re.findall(r'(?:public|private|protected)\s+\w+\s+(\w+)\s*\(', source_code_str)
                    clean_methods = [extract_clean_name(m, 'method') for m in methods]
                    
                    for method in clean_methods:
                        memory['method_signatures'][method] = {
                            'class': class_name,
                            'file_path': file_path
                        }
                    
                except Exception as e:
                    print(f"Error processing {file}: {e}")
                    continue
    
    return class_registry

def extract_ast_context_only(file_path):
    """Extract AST structure for context only (not for final output)"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            source_code = f.read().encode('utf-8')
        
        tree = parser.parse(source_code)
        root_node = tree.root_node
        
        context_info = []
        
        def traverse_for_context(node, parent_type=None, parent_name=None):
            if node.type == 'class_declaration':
                for child in node.children:
                    if child.type == 'identifier':
                        class_name = source_code[child.start_byte:child.end_byte].decode('utf-8')
                        context_info.append(f"class:{class_name}")
                        for grandchild in node.children:
                            traverse_for_context(grandchild, 'class', class_name)
                        break
            elif node.type == 'method_declaration' and parent_type == 'class':
                for child in node.children:
                    if child.type == 'identifier':
                        method_name = source_code[child.start_byte:child.end_byte].decode('utf-8')
                        context_info.append(f"method:{parent_name}.{method_name}")
                        break
            else:
                for child in node.children:
                    traverse_for_context(child, parent_type, parent_name)
        
        traverse_for_context(root_node)
        return "\n".join(context_info)
        
    except Exception as e:
        return f"AST parsing error: {e}"

# Execute Stage 3
class_registry = build_class_registry()

# Store class registry in memory
memory['class_registry'] = class_registry
save_memory(memory)

print(f"Stage 3 Complete: Class registry built with {len(class_registry)} classes")
print(f"Method signatures stored: {len(memory['method_signatures'])}")

# ========== STAGE 4: LLM PROCESSING WITH AST CONTEXT ==========

def build_optimized_prompt(file_path, class_name):
    # Build AST context for the specific file
    ast_context = extract_ast_context_only(file_path)
    
    # Build simplified prompt for structural extraction only
    prompt = f"""
You are a Java code lineage extraction engine focused on STRUCTURAL relationships only.

CURRENT FILE: {class_name}

AST CONTEXT (for reference only):
{ast_context}

EXTRACTION RULES - STRUCTURAL ONLY:

1. Extract ONLY basic structural relationships:
   - class -[declares]-> method  
   - class -[has_field]-> variable
   - method -[uses]-> variable
   - class -[declares]-> endpoint
   - class -[maps_to]-> table

2. Use SIMPLE names only (remove prefixes like method:, class:, etc.)
3. NEVER create reverse relationships (method->class, variable->method, etc.)
4. Clean node names (remove method:, class: prefixes)

Extract relationships in format:
[SourceType]:SourceName -[RELATIONSHIP]-> [TargetType]:TargetName

Return ONLY the structural triples, no explanations.
"""
    return prompt.replace("{", "{{").replace("}", "}}")

def smart_chunk_strategy(file_path, content):
    lines = content.count('\n') + 1
    
    # Escape curly braces to prevent LangChain template conflicts
    escaped_content = escape_braces_for_langchain(content)
    
    if lines <= 1000:
        return [{'content': escaped_content, 'metadata': {'source': file_path, 'chunk_type': 'whole_file'}}]
    else:
        splitter = RecursiveCharacterTextSplitter.from_language(
            language=LC_Language.JAVA,
            chunk_size=8000,
            chunk_overlap=400
        )
        doc = Document(page_content=escaped_content, metadata={'source': file_path})
        chunks = splitter.split_documents([doc])
        return [{'content': chunk.page_content, 'metadata': {**chunk.metadata, 'chunk_type': 'language_chunk'}} for chunk in chunks]

# Collect documents with smart chunking - LIGHTWEIGHT (no class_registry dependency)
smart_docs = []

# Process files directly from filesystem
for root, _, files in os.walk(BASE_PATH):
    for file in files:
        if file.endswith('.java'):
            file_path = os.path.join(root, file)
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Process ALL Java files - no filtering to ensure complete pipeline coverage
                
                class_name = extract_clean_name(file.replace('.java', ''), 'class')
                chunks = smart_chunk_strategy(file_path, content)
                
                for chunk in chunks:
                    chunk['metadata']['class_name'] = class_name
                smart_docs.extend(chunks)
                
            except Exception as e:
                print(f"Error reading {file_path}: {e}")
                continue

print(f"Total documents prepared: {len(smart_docs)} (processed directly from files)")
print(f"Stage 4 Optimization: No class_registry dependency - truly lightweight!")

# ========== STAGE 4B: TRULY LIGHTWEIGHT LLM EXTRACTION (NO CLASS_REGISTRY/MEMORY) ==========

all_llm_lineage = []

# Process each chunk independently - no batching needed
for doc_info in tqdm(smart_docs, desc='Stage 4B: Lightweight LLM Processing'):
    file_path = doc_info['metadata'].get('source')
    class_name = doc_info['metadata'].get('class_name', 'Unknown')
    
    # Only use AST context for this specific file - no heavy memory
    ast_context = extract_ast_context_only(file_path)
    
    # Lightweight prompt - only structural relationships
    enhanced_prompt = f"""
Extract ONLY basic structural relationships from this Java code chunk.

FILE: {class_name}
AST CONTEXT: {ast_context}

Extract only:
- class -[declares]-> method
- class -[has_field]-> variable  
- method -[uses]-> variable
- class -[declares]-> endpoint
- class -[maps_to]-> table
- method -[calls]-> method

⚠️ Naming Conventions (IMPORTANT):
- Always return identifiers (class, method, variable, endpoint, table names) in **PascalCase** (e.g., UserService, ConvertDtoToEntity).
- Do not use snake_case, camelCase, or lowercase.
- Do not include prefixes like `get`, `set`, `is`, or `do`.
- Strip generic wrappers like List<>, Map<> from variable types.
- Keep names clean and normalized.


Return only valid structural triples.
""".replace("{", "{{").replace("}", "}}")

    
    transformer = LLMGraphTransformer(
        llm=llm,
        additional_instructions=enhanced_prompt,
        allowed_nodes=['class', 'interface', 'method', 'variable', 'table', 'endpoint'],
        allowed_relationships=[
            ('class', 'declares', 'method'),
            ('interface', 'declares', 'method'),
            ('class', 'declares', 'endpoint'),
            ('method', 'calls', 'method'),
            ('class', 'has_field', 'variable'),
            ('method', 'uses', 'variable'),
            ('class', 'uses', 'class'),
            ('interface', 'extends', 'interface'),
            ('class', 'extends', 'class'),
            ('class', 'implements', 'interface'),
            ('class', 'maps_to', 'table'),
            ('method', 'reads_from', 'table'),
            ('method', 'writes_to', 'table')
        ],
        strict_mode=True,
        node_properties=False,
        relationship_properties=False
    )
    
    try:
        # Process only this chunk - no heavy memory usage
        escaped_content = escape_braces_for_langchain(doc_info['content'])
        doc = Document(page_content=escaped_content, metadata=doc_info['metadata'])
        graph_docs = transformer.convert_to_graph_documents([doc])
        
        for gd in graph_docs:
            for rel in gd.relationships:
                s_node = rel.source.id.strip()
                s_type = rel.source.type.strip().lower()
                t_node = rel.target.id.strip()
                t_type = rel.target.type.strip().lower()
                rel_type = rel.type.strip().lower()
                
                # Validate relationship direction
                valid_directions = {
                    ('class', 'declares', 'method'),
                    ('interface', 'declares', 'method'),
                    ('class', 'declares', 'endpoint'),
                    ('method', 'calls', 'method'),
                    ('class', 'has_field', 'variable'),
                    ('method', 'uses', 'variable'),
                    ('class', 'uses', 'class'),
                    ('interface', 'extends', 'interface'),
                    ('class', 'extends', 'class'),
                    ('class', 'implements', 'interface'),
                    ('class', 'maps_to', 'table'),
                    ('method', 'reads_from', 'table'),
                    ('method', 'writes_to', 'table')
                }
                
                # Check if current direction is valid
                if (s_type, rel_type, t_type) not in valid_directions:
                    # Check if reverse direction is valid
                    if (t_type, rel_type, s_type) in valid_directions:
                        s_node, t_node = t_node, s_node
                        s_type, t_type = t_type, s_type
                    else:
                        continue  # Skip invalid relationship
                    
                def to_pascal_case(name):
                    if not name:
                        return name
                    return ''.join(word.capitalize() for word in re.split(r'[_\-\s\.]', name))

                # In the loop
                s_node = to_pascal_case(s_node)
                t_node = to_pascal_case(t_node)


                # Normalize entity names
                s_node = extract_clean_name(s_node, s_type)
                t_node = extract_clean_name(t_node, t_type)

                # Filter out temp variables
                if t_type == 'variable' and is_temp_variable(t_node):
                    continue
                if s_type == 'variable' and is_temp_variable(s_node):
                    continue

                if not s_node or not t_node or s_node == t_node:
                    continue

                all_llm_lineage.append({
                    'source_node': s_node,
                    'source_type': s_type.title(),
                    'destination_node': t_node,
                    'destination_type': t_type.title(),
                    'relationship': rel_type.upper(),
                    'file_path': file_path
                })
                
    except Exception as e:
        print(f"LLM processing error for {file_path}: {e}")
        continue

df_llm_lineage = pd.DataFrame(all_llm_lineage)

# Store Stage 4 results in memory for Stage 5 batch processing
for _, row in df_llm_lineage.iterrows():
    edge_key = f"{row['source_node']}-{row['relationship']}-{row['destination_node']}"
    memory['validated_edges'].add(edge_key)
    
    # Store variable contexts for Stage 5 batch processing
    if row['destination_type'] == 'Variable':
        memory['variable_contexts'][row['destination_node']] = {
            'context': row['source_node'] if row['source_type'] in ['Method', 'Class'] else 'Unknown',
            'context_type': row['source_type'].lower() if row['source_type'] in ['Method', 'Class'] else 'class',
            'stage': '4b_structural'
        }

save_memory(memory)

print(f'Stage 4B Complete: {len(df_llm_lineage)} structural relationships extracted')
print(f'Memory usage: Lightweight - no heavy memory or registry used')
print(f'Variable contexts stored for Stage 5: {len([r for _, r in df_llm_lineage.iterrows() if r["destination_type"] == "Variable"])}')

df_llm_lineage

# ========== STAGE 5: BATCH PROCESSING WITH MEMORY OPTIMIZATION ==========

import gc

# Batch configuration for scalability
BATCH_SIZE = 50  # Process 50 classes at a time

# Initialize long-term memory for persistent storage
long_term_memory = {
    'all_validated_edges': set(),
    'global_variable_contexts': {},
    'global_method_signatures': {},
    'processed_classes': set()
}

def get_class_specific_memory(class_name, long_term_memory):
    """Extract only memory relevant to current class for lightweight processing"""
    relevant_memory = {
        'variable_contexts': {},
        'method_signatures': {}
    }
    
    # Only include memory related to current class
    for var, context in long_term_memory.get('global_variable_contexts', {}).items():
        if context.get('context') == class_name or context.get('class_context') == class_name:
            relevant_memory['variable_contexts'][var] = context
    
    for method, info in long_term_memory.get('global_method_signatures', {}).items():
        if info.get('class') == class_name:
            relevant_memory['method_signatures'][method] = info
    
    return relevant_memory

def build_lightweight_memory_context(relevant_memory):
    """Build memory context string from filtered memory"""
    memory_context = ''
    
    if relevant_memory.get('variable_contexts'):
        memory_context += f"Relevant Variable Contexts: {len(relevant_memory['variable_contexts'])} variables\n"
        for var_name, context in list(relevant_memory['variable_contexts'].items())[:3]:  # Show first 3
            memory_context += f"  - {var_name} (context: {context.get('context', 'Unknown')})\n"
    
    if relevant_memory.get('method_signatures'):
        memory_context += f"Relevant Methods: {len(relevant_memory['method_signatures'])} methods\n"
    
    return memory_context

# Prepare class batches
class_names = list(class_registry.keys())
class_batches = [class_names[i:i+BATCH_SIZE] for i in range(0, len(class_names), BATCH_SIZE)]

print(f"Stage 5 Setup: {len(class_names)} classes split into {len(class_batches)} batches of {BATCH_SIZE}")
print(f"Batch processing will prevent memory overflow for large codebases")

# ========== STAGE 5: BATCH EXECUTION WITH SHORT/LONG TERM MEMORY ==========

all_transformation_relationships = []

# Process each batch with memory optimization
for batch_num, batch_classes in enumerate(class_batches):
    print(f"\nProcessing batch {batch_num + 1}/{len(class_batches)} ({len(batch_classes)} classes)")
    
    # Initialize short-term memory for this batch only
    short_term_memory = {
        'variable_contexts': {},
        'method_signatures': {},
        'current_batch_edges': set()
    }
    
    batch_relationships = []
    
    # Process each class in the batch
    for class_name in tqdm(batch_classes, desc=f"Batch {batch_num + 1} Processing"):
        if class_name not in class_registry:
            continue
            
        class_info = class_registry[class_name]
        source_code = class_info['source_code']
        escaped_source_code = escape_braces_for_langchain(source_code)
        
        # Get class-specific memory context (lightweight)
        relevant_memory = get_class_specific_memory(class_name, long_term_memory)
        memory_context = build_lightweight_memory_context(relevant_memory)
        
        try:
            # Enhanced transformation prompt with lightweight memory context
            transformation_prompt = f"""
You are a Java data flow analysis engine for class: {class_name}

LIGHTWEIGHT MEMORY CONTEXT:
{memory_context}

EXTRACT DATA FLOW RELATIONSHIPS:

1. VARIABLE TRANSFORMATIONS & FLOWS:
   - Variable -[FLOWS_TO]-> Variable (data passing)
   - Variable -[TRANSFORMS_TO]-> Variable (data conversion)
   - Method -[PRODUCES]-> Variable (method creates variable)

2. DATABASE OPERATIONS:
   - Method -[READS_FROM]-> Table (SELECT operations)
   - Method -[WRITES_TO]-> Table (INSERT/UPDATE/DELETE)
   - Variable -[PERSISTS_TO]-> Table (entity persistence)

3. API RELATIONSHIPS:
   - Class -[EXPOSES]-> Endpoint (REST endpoints)
   - Method -[MAPS_TO]-> Endpoint (method-endpoint mapping)
   - Endpoint -[ACCEPTS]-> Variable (request parameters)
   - Endpoint -[RETURNS]-> Variable (response data)

4. METHOD OPERATIONS:
   - Method -[CALLS]-> Method (method invocations)
   - Method -[INVOKES]-> ExternalService (external API calls)

NAMING RULES:
- PascalCase for Methods, Classes, Endpoints
- Variables: only variable name (userDto, not CreateUser.userDto)
- Endpoints: include HTTP method (GET:/api/users)
- Tables: singular form (User, not Users)
- Filter temp variables (i, j, temp, tmp, counter)

CODE:
{escaped_source_code}

Return format: [SourceType]:SourceName -[RELATIONSHIP]-> [TargetType]:TargetName
"""
            
            response = llm.invoke(transformation_prompt)
            content = response.content if hasattr(response, 'content') else str(response)
            
            doc = Document(page_content=content, metadata={'class_name': class_name})
            
            transformer = LLMGraphTransformer(
                llm=llm,
                allowed_nodes=['variable', 'method', 'table', 'class', 'endpoint', 'database', 'externalservice'],
                allowed_relationships=[
                    ('variable', 'flows_to', 'variable'),
                    ('variable', 'transforms_to', 'variable'),
                    ('method', 'produces', 'variable'),
                    ('method', 'reads_from', 'table'),
                    ('method', 'writes_to', 'table'),
                    ('variable', 'persists_to', 'table'),
                    ('class', 'exposes', 'endpoint'),
                    ('method', 'maps_to', 'endpoint'),
                    ('endpoint', 'accepts', 'variable'),
                    ('endpoint', 'returns', 'variable'),
                    ('method', 'calls', 'method'),
                    ('method', 'invokes', 'externalservice')
                ],
                strict_mode=False,
                node_properties=False,
                relationship_properties=False,
            )
            
            graph_docs = transformer.convert_to_graph_documents([doc])
            
            for gd in graph_docs:
                for rel in gd.relationships:
                    s_type = rel.source.type.title()
                    t_type = rel.target.type.title()
                    s_node = extract_clean_name(rel.source.id, s_type.lower())
                    t_node = extract_clean_name(rel.target.id, t_type.lower())
                    rel_type = rel.type.upper()
                    
                    # Filter out temp variables
                    if s_type == 'Variable' and is_temp_variable(s_node):
                        continue
                    if t_type == 'Variable' and is_temp_variable(t_node):
                        continue
                    
                    if not s_node or not t_node or s_node == t_node:
                        continue
                    
                    relationship = {
                        'source_node': s_node,
                        'source_type': s_type,
                        'destination_node': t_node,
                        'destination_type': t_type,
                        'relationship': rel_type,
                        'class_context': class_name,
                        'stage': 'batch_transformations'
                    }
                    
                    batch_relationships.append(relationship)
                    
                    # Store in short-term memory for this batch
                    edge_key = f"{s_node}-{rel_type}-{t_node}"
                    short_term_memory['current_batch_edges'].add(edge_key)
                    
                    # Store variable context in short-term memory
                    for node_name, node_type in [(s_node, s_type), (t_node, t_type)]:
                        if node_type == 'Variable':
                            short_term_memory['variable_contexts'][node_name] = {
                                'context': class_name,
                                'context_type': 'class',
                                'stage': '5_batch_transformations'
                            }
            
        except Exception as llm_error:
            print(f"LLM transformation error for {class_name}: {llm_error}")
            continue
    
    # Move batch results to long-term memory
    long_term_memory['all_validated_edges'].update(short_term_memory['current_batch_edges'])
    long_term_memory['global_variable_contexts'].update(short_term_memory['variable_contexts'])
    long_term_memory['processed_classes'].update(batch_classes)
    
    # Add batch relationships to overall results
    all_transformation_relationships.extend(batch_relationships)
    
    print(f"Batch {batch_num + 1} complete: {len(batch_relationships)} relationships extracted")
    
    # MEMORY CLEANUP - Critical for scalability
    del short_term_memory
    del batch_relationships
    del relevant_memory
    gc.collect()  # Force garbage collection
    
    print(f"Memory cleaned for batch {batch_num + 1}")

# Create final DataFrame
df_transformations = pd.DataFrame(all_transformation_relationships)

print(f'\nStage 5 Complete: {len(df_transformations)} transformation relationships extracted')
print(f'Total classes processed: {len(long_term_memory["processed_classes"])}')
print(f'Total edges in long-term memory: {len(long_term_memory["all_validated_edges"])}')

# Summary statistics
transformations_count = len([r for r in all_transformation_relationships if 'TRANSFORMS_TO' in r.get('relationship', '')])
flows_count = len([r for r in all_transformation_relationships if 'FLOWS_TO' in r.get('relationship', '')])
produces_count = len([r for r in all_transformation_relationships if 'PRODUCES' in r.get('relationship', '')])
db_ops_count = len([r for r in all_transformation_relationships if r.get('relationship') in ['READS_FROM', 'WRITES_TO']])
method_calls_count = len([r for r in all_transformation_relationships if 'CALLS' in r.get('relationship', '')])

print(f'\nRelationship breakdown:')
print(f'Transformations: {transformations_count}, Flows: {flows_count}')
print(f'Produces: {produces_count}, DB Ops: {db_ops_count}')
print(f'Method Calls: {method_calls_count}')
print(f'Scalability: Processed in {len(class_batches)} batches with memory cleanup')

df_transformations

# ========== STAGE 6: FINAL CONSOLIDATION ==========

# Combine all DataFrames
all_dataframes = []

if len(df_hierarchy) > 0:
    all_dataframes.append(df_hierarchy)
    print(f"Hierarchy relationships: {len(df_hierarchy)}")
    
    # DEBUG: Show breakdown of hierarchy relationships
    declares_count = len(df_hierarchy[df_hierarchy['relationship'] == 'DECLARES'])
    contains_count = len(df_hierarchy[df_hierarchy['relationship'] == 'CONTAINS'])
    print(f"  - CONTAINS (folder-file): {contains_count}")
    print(f"  - DECLARES (file-class): {declares_count}")
    
    # DEBUG: Show actual file-class relationships in df_hierarchy
    file_class_in_hierarchy = df_hierarchy[
        (df_hierarchy['source_type'] == 'File') & 
        (df_hierarchy['destination_type'] == 'Class') & 
        (df_hierarchy['relationship'] == 'DECLARES')
    ]
    print(f"  - File-Class DECLARES in hierarchy: {len(file_class_in_hierarchy)}")
    if len(file_class_in_hierarchy) > 0:
        print("    Sample file-class relationships:")
        for _, row in file_class_in_hierarchy.head(3).iterrows():
            print(f"      {row['source_node']} ({row['source_type']}) -[{row['relationship']}]-> {row['destination_node']} ({row['destination_type']})")
    
    # DEBUG: Show ALL hierarchy relationships to see what's actually there
    print(f"\n🔍 DEBUG: ALL hierarchy relationships ({len(df_hierarchy)}):")
    for _, row in df_hierarchy.iterrows():
        print(f"  {row['source_node']} ({row['source_type']}) -[{row['relationship']}]-> {row['destination_node']} ({row['destination_type']})")

if len(df_llm_lineage) > 0:
    all_dataframes.append(df_llm_lineage)
    print(f"LLM lineage relationships: {len(df_llm_lineage)}")

if len(df_transformations) > 0:
    all_dataframes.append(df_transformations)
    print(f"Transformation relationships: {len(df_transformations)}")

# Consolidate all relationships
if all_dataframes:
    df_final = pd.concat(all_dataframes, ignore_index=True)
else:
    df_final = pd.DataFrame()

# Remove duplicates based on core relationship columns
df_final = df_final.drop_duplicates(subset=['source_node', 'source_type', 'destination_node', 'destination_type', 'relationship'])

# DEBUG: Check file-class relationships after deduplication
file_class_after_dedup = df_final[
    (df_final['source_type'] == 'File') & 
    (df_final['destination_type'] == 'Class') & 
    (df_final['relationship'] == 'DECLARES')
]
print(f"\n🔍 DEBUG: File-Class relationships after deduplication: {len(file_class_after_dedup)}")

# Filter to only allowed nodes and relationships
allowed_nodes = {'Folder', 'File', 'Class', 'Interface', 'Method', 'Variable', 'Table', 'Endpoint', 'Database', 'Externalservice'}
allowed_relationships = {
    'CONTAINS', 'DECLARES', 'HAS_FIELD', 'USES', 'CALLS', 'EXTENDS', 'IMPLEMENTS',
    'MAPS_TO', 'READS_FROM', 'WRITES_TO', 'FLOWS_TO', 'TRANSFORMS_TO', 'PRODUCES',
    'EXPOSES', 'ACCEPTS', 'RETURNS', 'INVOKES', 'PERSISTS_TO'
}

# Apply filters
df_final = df_final[
    (df_final['source_type'].isin(allowed_nodes)) &
    (df_final['destination_type'].isin(allowed_nodes)) &
    (df_final['relationship'].isin(allowed_relationships))
]

# DEBUG: Check file-class relationships after filtering
file_class_after_filter = df_final[
    (df_final['source_type'] == 'File') & 
    (df_final['destination_type'] == 'Class') & 
    (df_final['relationship'] == 'DECLARES')
]
print(f"🔍 DEBUG: File-Class relationships after filtering: {len(file_class_after_filter)}")

# CRITICAL FIX: Normalize all node names to consistent PascalCase
# This fixes issues like 'Userservice' vs 'UserService'
print("\n🔧 NORMALIZING: Applying consistent PascalCase to all node names...")

def normalize_node_name(name, node_type):
    """Ensure consistent PascalCase for folders, files, classes, methods"""
    if not name or pd.isna(name):
        return name
    
    # Apply PascalCase normalization for these types
    if node_type in ['Folder', 'File', 'Class', 'Interface', 'Method', 'Table']:
        return to_pascal_case(str(name))
    
    # Keep variables as-is (they should not be PascalCase)
    return str(name)

# Apply normalization
df_final['source_node'] = df_final.apply(lambda row: normalize_node_name(row['source_node'], row['source_type']), axis=1)
df_final['destination_node'] = df_final.apply(lambda row: normalize_node_name(row['destination_node'], row['destination_type']), axis=1)

print(f"✅ NORMALIZATION: Applied consistent PascalCase to {len(df_final)} relationships")

# Remove duplicates again after normalization (in case normalization created duplicates)
before_dedup = len(df_final)
df_final = df_final.drop_duplicates(subset=['source_node', 'source_type', 'destination_node', 'destination_type', 'relationship'])
after_dedup = len(df_final)
if before_dedup != after_dedup:
    print(f"🔧 DEDUPLICATION: Removed {before_dedup - after_dedup} duplicate relationships after normalization")


# Clean up column names and ensure consistency
required_columns = ['source_node', 'source_type', 'destination_node', 'destination_type', 'relationship']
for col in required_columns:
    if col not in df_final.columns:
        df_final[col] = ''

# Final cleaning and validation
df_final = df_final[df_final['source_node'].notna() & (df_final['source_node'] != '')]
df_final = df_final[df_final['destination_node'].notna() & (df_final['destination_node'] != '')]

file_class_after_filter = df_final[
    (df_final['source_type'] == 'File') & 
    (df_final['destination_type'] == 'Class') & 
    (df_final['relationship'] == 'DECLARES')
]
print(f"🔍 DEBUG: File-Class relationships after filtering: {len(file_class_after_filter)}")

# CRITICAL FIX: Don't filter out File-Class DECLARES relationships even if they have the same name
# This is valid: UserService.java (File) -[DECLARES]-> UserService (Class)
file_class_declares = (
    (df_final['source_type'] == 'File') & 
    (df_final['destination_type'] == 'Class') & 
    (df_final['relationship'] == 'DECLARES')
)

# Filter out self-referential relationships EXCEPT for valid File-Class DECLARES
df_final = df_final[
    (df_final['source_node'] != df_final['destination_node']) | file_class_declares
]

# DEBUG: Check file-class relationships after final cleaning
file_class_final = df_final[
    (df_final['source_type'] == 'File') & 
    (df_final['destination_type'] == 'Class') & 
    (df_final['relationship'] == 'DECLARES')
]
print(f"🎯 FIXED: File-Class relationships after final cleaning: {len(file_class_final)}")
if len(file_class_final) > 0:
    print("✅ File-Class relationships preserved:")
    for _, row in file_class_final.iterrows():
        print(f"  {row['source_node']} ({row['source_type']}) -[{row['relationship']}]-> {row['destination_node']} ({row['destination_type']})")

# Save to CSV
csv_filename = 'java_lineage_test_apps.csv'
df_final[required_columns].to_csv(csv_filename, index=False)

print(f"\nFinal consolidation complete:")
print(f"Total relationships: {len(df_final)}")
print(f"CSV saved: {csv_filename}")


# Summary by relationship type
relationship_summary = df_final['relationship'].value_counts()
print("\nRelationship breakdown:")
for rel_type, count in relationship_summary.items():
    print(f"  {rel_type}: {count}")


# Summary by node type
print("\nNode type summary:")
source_types = df_final['source_type'].value_counts()
dest_types = df_final['destination_type'].value_counts()
all_types = (source_types + dest_types).fillna(0).astype(int)
for node_type, count in all_types.items():
    print(f"  {node_type}: {count}")

df_final

# ========== STAGE 7: NEO4J UPLOAD ==========

def upload_to_neo4j_fixed(df_final):
    # CRITICAL FIX: Use (name, type) tuples to handle nodes with same name but different types
    # This prevents the bug where UserService (File) gets overwritten by UserService (Class)
    unique_nodes = set()  # Set of (node_name, node_type) tuples
    
    for _, row in df_final.iterrows():
        source_node = row['source_node']
        dest_node = row['destination_node']
        source_type = row['source_type']
        dest_type = row['destination_type']
        
        # FIXED: Store as (name, type) tuples to handle same names with different types
        unique_nodes.add((source_node, source_type))
        unique_nodes.add((dest_node, dest_type))
    
    print(f"🎯 FIXED: Creating {len(unique_nodes)} unique (name, type) nodes...")
    
    # Create nodes with proper labels and metadata
    for node_name, node_type in tqdm(unique_nodes, desc="Creating nodes"):
        
        # Add variable context metadata if available
        metadata = {}
        if node_type == 'Variable' and node_name in memory.get('variable_contexts', {}):
            context_info = memory['variable_contexts'][node_name]
            metadata['defining_context'] = context_info.get('context', 'Unknown')
            metadata['context_type'] = context_info.get('context_type', 'Unknown')
        
        # Create node with metadata
        properties_str = ', '.join([f"{k}: '{v}'" for k, v in metadata.items()])
        properties_clause = f", {properties_str}" if properties_str else ""
        
        create_query = f"""
        MERGE (n:{node_type} {{name: '{node_name}'{properties_clause}}})
        """
        
        try:
            graph.query(create_query)
        except Exception as e:
            print(f"Error creating node {node_name} ({node_type}): {e}")
    
    # Create relationships
    for _, row in tqdm(df_final.iterrows(), desc="Creating relationships", total=len(df_final)):
        source_node = row['source_node']
        dest_node = row['destination_node']
        source_type = row['source_type']
        dest_type = row['destination_type']
        relationship = row['relationship']
        
        create_rel_query = f"""
        MATCH (s:{source_type} {{name: '{source_node}'}})
        MATCH (t:{dest_type} {{name: '{dest_node}'}})
        MERGE (s)-[:{relationship}]->(t)
        """
        
        try:
            graph.query(create_rel_query)
        except Exception as e:
            print(f"Error creating relationship {source_node}-{relationship}->{dest_node}: {e}")
    
    print(f"✅ Neo4j upload complete: {len(unique_nodes)} unique nodes, {len(df_final)} relationships")
    print(f"📊 Node breakdown by type:")
    type_counts = {}
    for _, node_type in unique_nodes:
        type_counts[node_type] = type_counts.get(node_type, 0) + 1
    for node_type, count in sorted(type_counts.items()):
        print(f"  {node_type}: {count} nodes")

# DEBUG: Final check BEFORE Neo4j upload
file_class_before_upload = df_final[
    (df_final['source_type'] == 'File') & 
    (df_final['destination_type'] == 'Class') & 
    (df_final['relationship'] == 'DECLARES')
]
print(f"\n🔍 DEBUG: File-Class relationships BEFORE Neo4j upload: {len(file_class_before_upload)}")
if len(file_class_before_upload) > 0:
    print("File-Class relationships to upload:")
    for _, row in file_class_before_upload.iterrows():
        print(f"  {row['source_node']} ({row['source_type']}) -[{row['relationship']}]-> {row['destination_node']} ({row['destination_type']})")

# FINAL NORMALIZATION: Apply PascalCase to all node names before Neo4j upload
print("\n🔧 FINAL NORMALIZATION: Converting all folder/file/class/method names to PascalCase before Neo4j upload...")

def final_pascal_case_normalization(name, node_type):
    """Final PascalCase normalization before Neo4j upload"""
    if not name or pd.isna(name):
        return name
    
    name_str = str(name).strip()
    if not name_str:
        return name_str
    
    # AGGRESSIVE FIX: Apply PascalCase to these node types
    if node_type in ['Folder', 'File', 'Class', 'Interface', 'Method', 'Table']:
        # AGGRESSIVE FIX: Handle specific problematic cases for Class and Method names
        if node_type in ['Class', 'Method']:
            # Handle specific patterns that are causing issues
            
            # Fix 'Userservice' -> 'UserService'
            if 'service' in name_str.lower():
                name_str = re.sub(r'([a-z])service', r'\\1Service', name_str, flags=re.IGNORECASE)
            
            # Fix 'Createuser' -> 'CreateUser'
            if 'user' in name_str.lower():
                name_str = re.sub(r'([a-z])user', r'\\1User', name_str, flags=re.IGNORECASE)
            
            # Fix 'Createorder' -> 'CreateOrder'
            if 'order' in name_str.lower():
                name_str = re.sub(r'([a-z])order', r'\\1Order', name_str, flags=re.IGNORECASE)
            
            # Fix 'Getuser' -> 'GetUser'
            if name_str.lower().startswith('get') and 'user' in name_str.lower():
                name_str = re.sub(r'^get([a-z])', r'Get\\1', name_str, flags=re.IGNORECASE)
            
            # Fix 'Getorder' -> 'GetOrder'
            if name_str.lower().startswith('get') and 'order' in name_str.lower():
                name_str = re.sub(r'^get([a-z])', r'Get\\1', name_str, flags=re.IGNORECASE)
            
            # General camelCase splitting and proper capitalization
            # Split on capital letters and rejoin with proper capitalization
            parts = re.findall(r'[A-Z]?[a-z]+|[A-Z]+(?=[A-Z][a-z]|\\b)', name_str)
            if parts and len(parts) > 1:
                result = ''.join(part.capitalize() for part in parts)
                return result
            else:
                # Single word - ensure first letter is capitalized
                return name_str[0].upper() + name_str[1:] if len(name_str) > 1 else name_str.upper()
        
        # For other types (Folder, File, Table), use standard to_pascal_case
        return to_pascal_case(name_str)
    
    # Keep variables and other types as-is
    return name_str

# Apply final normalization to df_final
original_source_names = df_final['source_node'].tolist()
original_dest_names = df_final['destination_node'].tolist()

df_final['source_node'] = df_final.apply(lambda row: final_pascal_case_normalization(row['source_node'], row['source_type']), axis=1)
df_final['destination_node'] = df_final.apply(lambda row: final_pascal_case_normalization(row['destination_node'], row['destination_type']), axis=1)

# Check for changes
changes_made = 0
for i, (orig_src, orig_dest) in enumerate(zip(original_source_names, original_dest_names)):
    new_src = df_final.iloc[i]['source_node']
    new_dest = df_final.iloc[i]['destination_node']
    if orig_src != new_src:
        print(f"  📝 Normalized: '{orig_src}' → '{new_src}' ({df_final.iloc[i]['source_type']})")
        changes_made += 1
    if orig_dest != new_dest:
        print(f"  📝 Normalized: '{orig_dest}' → '{new_dest}' ({df_final.iloc[i]['destination_type']})")
        changes_made += 1

print(f"✅ FINAL NORMALIZATION: Made {changes_made} name corrections")

# Remove any duplicates created by normalization
before_final_dedup = len(df_final)
df_final = df_final.drop_duplicates(subset=['source_node', 'source_type', 'destination_node', 'destination_type', 'relationship'])
after_final_dedup = len(df_final)
if before_final_dedup != after_final_dedup:
    print(f"🔧 FINAL DEDUPLICATION: Removed {before_final_dedup - after_final_dedup} duplicate relationships")

# FINAL CLEANUP: Remove orphaned nodes before Neo4j upload
print("\n🧹 FINAL CLEANUP: Removing orphaned nodes...")

# Get all nodes and their connection counts
all_nodes = set()
for _, row in df_final.iterrows():
    all_nodes.add((row['source_node'], row['source_type']))
    all_nodes.add((row['destination_node'], row['destination_type']))

# Count connections for each node
node_connections = {}
for node_name, node_type in all_nodes:
    count = len(df_final[
        ((df_final['source_node'] == node_name) & (df_final['source_type'] == node_type)) |
        ((df_final['destination_node'] == node_name) & (df_final['destination_type'] == node_type))
    ])
    node_connections[(node_name, node_type)] = count

# Find nodes with similar names (case-insensitive) and keep the one with more connections
nodes_to_remove = set()
processed = set()

for (node_name, node_type), connections in node_connections.items():
    if (node_name.lower(), node_type) in processed:
        continue
    
    # Find all nodes with the same name (case-insensitive) and type
    similar_nodes = [(n, t, c) for (n, t), c in node_connections.items() 
                     if n.lower() == node_name.lower() and t == node_type]
    
    if len(similar_nodes) > 1:
        # Sort by connection count (descending), then by name length (ascending)
        similar_nodes.sort(key=lambda x: (-x[2], len(x[0])))
        
        # Keep the first one (most connections), remove others
        keep_node = similar_nodes[0]
        remove_nodes = similar_nodes[1:]
        
        print(f"  ✅ Keeping: '{keep_node[0]}' ({keep_node[1]}) - {keep_node[2]} connections")
        
        for remove_name, remove_type, remove_count in remove_nodes:
            nodes_to_remove.add((remove_name, remove_type))
            print(f"  🗑️ Removing: '{remove_name}' ({remove_type}) - {remove_count} connections")
    
    processed.add((node_name.lower(), node_type))

# Remove relationships involving orphaned nodes
if nodes_to_remove:
    before_cleanup = len(df_final)
    for remove_name, remove_type in nodes_to_remove:
        df_final = df_final[
            ~((df_final['source_node'] == remove_name) & (df_final['source_type'] == remove_type)) &
            ~((df_final['destination_node'] == remove_name) & (df_final['destination_type'] == remove_type))
        ]
    after_cleanup = len(df_final)
    print(f"\n🧹 CLEANUP COMPLETE: Removed {before_cleanup - after_cleanup} relationships with {len(nodes_to_remove)} duplicate nodes")
else:
    print(f"\n✅ NO DUPLICATES: All nodes are unique")

# Execute Neo4j upload
upload_to_neo4j_fixed(df_final)

# Final memory save
save_memory(memory)

print("\n========== PIPELINE COMPLETE ==========")
print(f"Total processing stages: 7")
print(f"Final relationships: {len(df_final)}")
print(f"Memory file: {MEMORY_FILE}")
print(f"CSV output: java_lineage_v9.csv")
print(f"Neo4j database: {NEO4J_DB}")

# DEBUG: Verify file-class relationships in final output
file_class_in_final = df_final[
    (df_final['source_type'] == 'File') & 
    (df_final['destination_type'] == 'Class') & 
    (df_final['relationship'] == 'DECLARES')
]
print(f"\n🔍 DEBUG: File-Class relationships in final output: {len(file_class_in_final)}")
if len(file_class_in_final) > 0:
    print("Sample file-class relationships:")
    for _, row in file_class_in_final.head(5).iterrows():
        print(f"  {row['source_node']} -[DECLARES]-> {row['destination_node']}")
else:
    print("❌ NO file-class relationships found in final output!")
    print("\nChecking what relationships ARE in final output:")
    rel_summary = df_final['relationship'].value_counts()
    for rel, count in rel_summary.head(10).items():
        print(f"  {rel}: {count}")

print("========================================")

# Step 1: Normalize all node names (before dedup)
df_final['source_node'] = df_final.apply(lambda row: normalize_node_name(row['source_node'], row['source_type']), axis=1)
df_final['destination_node'] = df_final.apply(lambda row: normalize_node_name(row['destination_node'], row['destination_type']), axis=1)

# Step 2: Remove exact duplicate edges (all fields considered)
df_final = df_final.drop_duplicates(subset=[
    'source_node', 'source_type',
    'destination_node', 'destination_type',
    'relationship'
])

# Step 3: Canonicalize (name.lower(), type) to remove duplicates due to casing
def deduplicate_nodes_by_lower_name(df):
    node_pairs = []
    for _, row in df.iterrows():
        node_pairs.extend([
            (row['source_node'], row['source_type']),
            (row['destination_node'], row['destination_type'])
        ])
    
    # Group by lowercase names + type
    from collections import defaultdict
    node_map = defaultdict(list)
    for name, typ in node_pairs:
        key = (name.lower(), typ)
        node_map[key].append(name)

    # Resolve name conflicts
    resolved_names = {}
    for key, variants in node_map.items():
        canonical = sorted(variants, key=lambda x: (len(x), x))[0]
        for v in variants:
            resolved_names[(v, key[1])] = canonical

    # Replace in df
    df['source_node'] = df.apply(lambda row: resolved_names.get((row['source_node'], row['source_type']), row['source_node']), axis=1)
    df['destination_node'] = df.apply(lambda row: resolved_names.get((row['destination_node'], row['destination_type']), row['destination_node']), axis=1)

    return df

df_final = deduplicate_nodes_by_lower_name(df_final)


df_final.info()

df_final[df_final["source_type"]=="Class"].head(5)